# Generated by Django 4.2.21 on 2025-06-11 09:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('rides', '0002_ridepending'),
        ('accounts', '0003_alter_user_role_passenger_driver_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='vehicle',
            name='front_photo',
            field=models.ImageField(blank=True, null=True, upload_to='vehicles/photos/'),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='has_gps',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='has_phone_charger',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='interior_photo',
            field=models.ImageField(blank=True, null=True, upload_to='vehicles/photos/'),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='is_verified',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='side_photo',
            field=models.ImageField(blank=True, null=True, upload_to='vehicles/photos/'),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='verification_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='vehicle',
            name='verification_notes',
            field=models.TextField(blank=True),
        ),
        migrations.CreateModel(
            name='RideRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.PositiveIntegerField(choices=[(1, '1 Star'), (2, '2 Stars'), (3, '3 Stars'), (4, '4 Stars'), (5, '5 Stars')])),
                ('review', models.TextField(blank=True)),
                ('punctuality', models.PositiveIntegerField(blank=True, choices=[(1, '1 Star'), (2, '2 Stars'), (3, '3 Stars'), (4, '4 Stars'), (5, '5 Stars')], null=True)),
                ('communication', models.PositiveIntegerField(blank=True, choices=[(1, '1 Star'), (2, '2 Stars'), (3, '3 Stars'), (4, '4 Stars'), (5, '5 Stars')], null=True)),
                ('cleanliness', models.PositiveIntegerField(blank=True, choices=[(1, '1 Star'), (2, '2 Stars'), (3, '3 Stars'), (4, '4 Stars'), (5, '5 Stars')], null=True)),
                ('safety', models.PositiveIntegerField(blank=True, choices=[(1, '1 Star'), (2, '2 Stars'), (3, '3 Stars'), (4, '4 Stars'), (5, '5 Stars')], null=True)),
                ('is_anonymous', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('rated_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings_received', to=settings.AUTH_USER_MODEL)),
                ('rater', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings_given', to=settings.AUTH_USER_MODEL)),
                ('ride', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='rides.ride')),
            ],
            options={
                'db_table': 'ride_ratings',
                'indexes': [models.Index(fields=['rated_user', '-created_at'], name='ride_rating_rated_u_412ca1_idx'), models.Index(fields=['rater', '-created_at'], name='ride_rating_rater_i_09b8dd_idx'), models.Index(fields=['ride'], name='ride_rating_ride_id_5154b2_idx')],
                'unique_together': {('ride', 'rater', 'rated_user')},
            },
        ),
    ]
