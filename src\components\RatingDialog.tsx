import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Star } from "lucide-react";
import { toast } from "sonner";

interface RatingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  rideId: string;
  ratedUserId: string;
  ratedUserName: string;
  userRole: 'driver' | 'passenger';
}

interface RatingData {
  rating: number;
  review: string;
  punctuality: number;
  communication: number;
  cleanliness: number;
  safety: number;
  is_anonymous: boolean;
}

const RatingDialog = ({ 
  isOpen, 
  onClose, 
  rideId, 
  ratedUserId, 
  ratedUserName, 
  userRole 
}: RatingDialogProps) => {
  const [ratingData, setRatingData] = useState<RatingData>({
    rating: 0,
    review: "",
    punctuality: 0,
    communication: 0,
    cleanliness: 0,
    safety: 0,
    is_anonymous: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleStarClick = (field: keyof RatingData, value: number) => {
    setRatingData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const renderStars = (field: keyof RatingData, label: string) => {
    const currentRating = ratingData[field] as number;
    
    return (
      <div className="space-y-2">
        <Label className="text-sm font-medium">{label}</Label>
        <div className="flex gap-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              onClick={() => handleStarClick(field, star)}
              className="focus:outline-none"
            >
              <Star
                className={`w-6 h-6 transition-colors ${
                  star <= currentRating
                    ? "fill-yellow-400 text-yellow-400"
                    : "text-gray-300 hover:text-yellow-400"
                }`}
              />
            </button>
          ))}
        </div>
      </div>
    );
  };

  const handleSubmit = async () => {
    if (ratingData.rating === 0) {
      toast.error("Please provide an overall rating");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('http://localhost:8000/api/auth/ratings/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ride: rideId,
          rated_user: ratedUserId,
          ...ratingData,
        }),
      });

      if (response.ok) {
        toast.success("Rating submitted successfully!");
        onClose();
        // Reset form
        setRatingData({
          rating: 0,
          review: "",
          punctuality: 0,
          communication: 0,
          cleanliness: 0,
          safety: 0,
          is_anonymous: false,
        });
      } else {
        const errorData = await response.json();
        toast.error(errorData.detail || "Failed to submit rating");
      }
    } catch (error) {
      console.error("Error submitting rating:", error);
      toast.error("Failed to submit rating");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRatingPrompt = () => {
    if (userRole === 'driver') {
      return `Rate your passenger: ${ratedUserName}`;
    } else {
      return `Rate your driver: ${ratedUserName}`;
    }
  };

  const getDetailedRatingLabels = () => {
    if (userRole === 'driver') {
      return {
        punctuality: "Punctuality",
        communication: "Communication",
        cleanliness: "Cleanliness",
        safety: "Respectfulness",
      };
    } else {
      return {
        punctuality: "Punctuality",
        communication: "Communication", 
        cleanliness: "Vehicle Cleanliness",
        safety: "Safe Driving",
      };
    }
  };

  const labels = getDetailedRatingLabels();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Rate Your Experience</DialogTitle>
          <DialogDescription>
            {getRatingPrompt()}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Overall Rating */}
          {renderStars('rating', 'Overall Rating *')}

          {/* Detailed Ratings */}
          <div className="grid grid-cols-2 gap-4">
            {renderStars('punctuality', labels.punctuality)}
            {renderStars('communication', labels.communication)}
            {renderStars('cleanliness', labels.cleanliness)}
            {renderStars('safety', labels.safety)}
          </div>

          {/* Review */}
          <div className="space-y-2">
            <Label htmlFor="review">Review (Optional)</Label>
            <Textarea
              id="review"
              placeholder={`Share your experience with ${ratedUserName}...`}
              value={ratingData.review}
              onChange={(e) => setRatingData(prev => ({ ...prev, review: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Anonymous Option */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="anonymous"
              checked={ratingData.is_anonymous}
              onChange={(e) => setRatingData(prev => ({ ...prev, is_anonymous: e.target.checked }))}
              className="rounded border-gray-300"
            />
            <Label htmlFor="anonymous" className="text-sm">
              Submit anonymously
            </Label>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : "Submit Rating"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RatingDialog;
