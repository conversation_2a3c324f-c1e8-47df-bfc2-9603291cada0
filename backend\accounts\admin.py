from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, UserProfile, Vehicle, Driver, Passenger, RideRating


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin configuration for User model"""
    
    list_display = [
        'email', 'username', 'full_name', 'city', 'rating',
        'rides_offered', 'rides_completed', 'loyalty_points',
        'is_verified', 'is_active', 'created_at'
    ]
    list_filter = [
        'is_active', 'is_verified', 'city', 'country',
        'email_notifications', 'sms_notifications', 'created_at'
    ]
    search_fields = ['email', 'username', 'full_name', 'phone_number']
    ordering = ['-created_at']
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('Personal Information', {
            'fields': (
                'full_name', 'phone_number', 'date_of_birth',
                'bio', 'avatar', 'city', 'country'
            )
        }),
        ('Ride Statistics', {
            'fields': (
                'rides_offered', 'rides_completed', 'loyalty_points',
                'referrals', 'rating', 'total_ratings'
            )
        }),
        ('Account Settings', {
            'fields': (
                'is_verified', 'email_notifications', 'sms_notifications'
            )
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """Admin configuration for UserProfile model"""
    
    list_display = [
        'user', 'preferred_language', 'preferred_currency',
        'is_driver', 'created_at'
    ]
    list_filter = ['preferred_language', 'preferred_currency', 'is_driver']
    search_fields = ['user__email', 'user__full_name']
    
    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Preferences', {
            'fields': ('preferred_language', 'preferred_currency')
        }),
        ('Driver Information', {
            'fields': (
                'is_driver', 'driver_license_number', 'driver_license_expiry'
            )
        }),
        ('Emergency Contact', {
            'fields': ('emergency_contact_name', 'emergency_contact_phone')
        }),
        ('Social Links', {
            'fields': ('facebook_profile', 'linkedin_profile')
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Vehicle)
class VehicleAdmin(admin.ModelAdmin):
    """Admin configuration for Vehicle model"""
    
    list_display = [
        'license_plate', 'driver', 'make', 'model', 'year',
        'color', 'seats', 'is_active', 'created_at'
    ]
    list_filter = [
        'make', 'vehicle_type', 'year', 'has_ac',
        'has_music', 'is_smoking_allowed', 'is_active'
    ]
    search_fields = [
        'license_plate', 'driver__user__email', 'driver__user__full_name',
        'make', 'model'
    ]
    
    fieldsets = (
        ('Driver', {
            'fields': ('driver',)
        }),
        ('Vehicle Information', {
            'fields': (
                'make', 'model', 'year', 'color',
                'license_plate', 'vehicle_type', 'seats'
            )
        }),
        ('Features', {
            'fields': (
                'has_ac', 'has_music', 'is_smoking_allowed', 'has_wifi'
            )
        }),
        ('Legal Documents', {
            'fields': ('insurance_expiry', 'registration_expiry')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Driver)
class DriverAdmin(admin.ModelAdmin):
    """Admin configuration for Driver model"""

    list_display = [
        'user', 'driver_license_number', 'license_expiry_date',
        'total_rides_offered', 'total_earnings', 'is_license_verified', 'created_at'
    ]
    list_filter = ['is_license_verified', 'license_expiry_date', 'created_at']
    search_fields = [
        'user__email', 'user__full_name', 'driver_license_number'
    ]

    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('License Information', {
            'fields': ('driver_license_number', 'license_expiry_date', 'is_license_verified', 'verification_date')
        }),
        ('Statistics', {
            'fields': ('total_rides_offered', 'total_earnings')
        }),
    )

    readonly_fields = ['created_at', 'updated_at', 'verification_date']


@admin.register(Passenger)
class PassengerAdmin(admin.ModelAdmin):
    """Admin configuration for Passenger model"""

    list_display = [
        'user', 'id_card_number', 'total_rides_booked',
        'total_spent', 'created_at'
    ]
    list_filter = ['smoking_preference', 'created_at']
    search_fields = [
        'user__email', 'user__full_name', 'id_card_number'
    ]

    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Identification', {
            'fields': ('id_card_number',)
        }),
        ('Statistics', {
            'fields': ('total_rides_booked', 'total_spent')
        }),
        ('Preferences', {
            'fields': ('preferred_car_type', 'smoking_preference')
        }),
    )

    readonly_fields = ['created_at', 'updated_at']


@admin.register(RideRating)
class RideRatingAdmin(admin.ModelAdmin):
    """Admin configuration for RideRating model"""

    list_display = [
        'ride', 'rater', 'rated_user', 'rating',
        'punctuality', 'communication', 'cleanliness', 'safety',
        'is_anonymous', 'created_at'
    ]
    list_filter = [
        'rating', 'punctuality', 'communication', 'cleanliness', 'safety',
        'is_anonymous', 'created_at'
    ]
    search_fields = [
        'rater__email', 'rater__full_name',
        'rated_user__email', 'rated_user__full_name',
        'ride__origin', 'ride__destination'
    ]

    fieldsets = (
        ('Rating Information', {
            'fields': ('ride', 'rater', 'rated_user', 'rating', 'review')
        }),
        ('Detailed Ratings', {
            'fields': ('punctuality', 'communication', 'cleanliness', 'safety')
        }),
        ('Settings', {
            'fields': ('is_anonymous',)
        }),
    )

    readonly_fields = ['created_at', 'updated_at']
