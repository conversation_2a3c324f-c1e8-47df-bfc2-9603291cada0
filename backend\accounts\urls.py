from django.urls import path
from . import views

urlpatterns = [
    # Authentication
    path('register/', views.UserRegistrationView.as_view(), name='user-register'),
    path('register/driver/', views.DriverRegistrationView.as_view(), name='driver-register'),
    path('register/passenger/', views.PassengerRegistrationView.as_view(), name='passenger-register'),
    path('login/', views.login_view, name='user-login'),
    path('logout/', views.logout_view, name='user-logout'),
    
    # User profile
    path('profile/', views.UserProfileView.as_view(), name='user-profile'),
    path('profile/update/', views.UserUpdateView.as_view(), name='user-update'),
    path('profile/details/', views.UserProfileDetailView.as_view(), name='user-profile-details'),
    path('change-password/', views.change_password_view, name='change-password'),
    
    # User statistics
    path('stats/', views.UserStatsView.as_view(), name='user-stats'),
    path('loyalty-points/', views.add_loyalty_points_view, name='add-loyalty-points'),
    
    # Vehicles
    path('vehicles/', views.VehicleListCreateView.as_view(), name='vehicle-list-create'),
    path('vehicles/<int:pk>/', views.VehicleDetailView.as_view(), name='vehicle-detail'),

    # Ratings
    path('ratings/', views.RideRatingListCreateView.as_view(), name='rating-list-create'),
    path('users/<int:user_id>/ratings/', views.UserRatingsView.as_view(), name='user-ratings'),

    # Dashboards
    path('driver/dashboard/', views.driver_dashboard_view, name='driver-dashboard'),
    path('passenger/dashboard/', views.passenger_dashboard_view, name='passenger-dashboard'),

    # Public user information
    path('users/<int:user_id>/', views.user_detail_view, name='user-detail'),
]
