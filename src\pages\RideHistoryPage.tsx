import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import RatingDialog from "@/components/RatingDialog";
import { useAuth } from "@/hooks/use-auth";
import { Star, MapPin, Clock, Users, DollarSign } from "lucide-react";

interface RideHistoryItem {
  id: string;
  origin: string;
  destination: string;
  departure_time: string;
  arrival_time: string;
  price: number;
  status: string;
  driver?: {
    id: string;
    full_name: string;
    rating: number;
  };
  passenger?: {
    id: string;
    full_name: string;
    rating: number;
  };
  seats_booked?: number;
  seats_available?: number;
  car_model?: string;
  car_color?: string;
  can_rate?: boolean;
  rating_given?: boolean;
}

const RideHistoryPage = () => {
  const { user } = useAuth();
  const [rideHistory, setRideHistory] = useState<RideHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [ratingDialogOpen, setRatingDialogOpen] = useState(false);
  const [selectedRide, setSelectedRide] = useState<RideHistoryItem | null>(null);

  useEffect(() => {
    if (user) {
      fetchRideHistory();
    }
  }, [user]);

  const fetchRideHistory = async () => {
    try {
      const endpoint = user?.role === 'driver' 
        ? 'http://localhost:8000/api/rides/driver/history/'
        : 'http://localhost:8000/api/rides/passenger/history/';
      
      const response = await fetch(endpoint, {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setRideHistory(data);
      } else {
        toast.error("Failed to load ride history");
      }
    } catch (error) {
      console.error("Error fetching ride history:", error);
      toast.error("Failed to load ride history");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRateUser = (ride: RideHistoryItem) => {
    setSelectedRide(ride);
    setRatingDialogOpen(true);
  };

  const handleRatingSubmitted = () => {
    setRatingDialogOpen(false);
    setSelectedRide(null);
    fetchRideHistory(); // Refresh to update rating status
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'default';
      case 'cancelled':
        return 'destructive';
      case 'active':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container mx-auto py-10 flex-1">
          <div className="text-center">Loading ride history...</div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container mx-auto py-10 flex-1">
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold">Ride History</h1>
          </div>

          <Tabs defaultValue="all" className="space-y-4">
            <TabsList>
              <TabsTrigger value="all">All Rides</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-4">
              <RideHistoryList 
                rides={rideHistory} 
                userRole={user?.role}
                onRateUser={handleRateUser}
              />
            </TabsContent>

            <TabsContent value="completed" className="space-y-4">
              <RideHistoryList 
                rides={rideHistory.filter(ride => ride.status === 'completed')} 
                userRole={user?.role}
                onRateUser={handleRateUser}
              />
            </TabsContent>

            <TabsContent value="cancelled" className="space-y-4">
              <RideHistoryList 
                rides={rideHistory.filter(ride => ride.status === 'cancelled')} 
                userRole={user?.role}
                onRateUser={handleRateUser}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {selectedRide && (
        <RatingDialog
          isOpen={ratingDialogOpen}
          onClose={() => setRatingDialogOpen(false)}
          rideId={selectedRide.id}
          ratedUserId={user?.role === 'driver' ? selectedRide.passenger?.id || '' : selectedRide.driver?.id || ''}
          ratedUserName={user?.role === 'driver' ? selectedRide.passenger?.full_name || '' : selectedRide.driver?.full_name || ''}
          userRole={user?.role || 'passenger'}
        />
      )}

      <Footer />
    </div>
  );
};

interface RideHistoryListProps {
  rides: RideHistoryItem[];
  userRole?: string;
  onRateUser: (ride: RideHistoryItem) => void;
}

const RideHistoryList = ({ rides, userRole, onRateUser }: RideHistoryListProps) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'default';
      case 'cancelled':
        return 'destructive';
      case 'active':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (rides.length === 0) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center text-muted-foreground">
            <Car className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No rides found</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {rides.map((ride) => (
        <Card key={ride.id}>
          <CardContent className="pt-6">
            <div className="flex justify-between items-start">
              <div className="space-y-3 flex-1">
                {/* Route */}
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <span className="font-medium">{ride.origin}</span>
                  <span className="text-muted-foreground">→</span>
                  <span className="font-medium">{ride.destination}</span>
                </div>

                {/* Date and Time */}
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    <span>{formatDate(ride.departure_time)}</span>
                  </div>
                  {ride.seats_booked && (
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      <span>{ride.seats_booked} seat{ride.seats_booked > 1 ? 's' : ''}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-1">
                    <DollarSign className="w-4 h-4" />
                    <span>{ride.price} TND</span>
                  </div>
                </div>

                {/* User Info */}
                <div className="flex items-center gap-2">
                  {userRole === 'driver' && ride.passenger ? (
                    <>
                      <span className="text-sm text-muted-foreground">Passenger:</span>
                      <span className="text-sm font-medium">{ride.passenger.full_name}</span>
                      <div className="flex items-center gap-1">
                        <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs">{ride.passenger.rating}</span>
                      </div>
                    </>
                  ) : userRole === 'passenger' && ride.driver ? (
                    <>
                      <span className="text-sm text-muted-foreground">Driver:</span>
                      <span className="text-sm font-medium">{ride.driver.full_name}</span>
                      <div className="flex items-center gap-1">
                        <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs">{ride.driver.rating}</span>
                      </div>
                    </>
                  )}
                </div>

                {/* Vehicle Info for passengers */}
                {userRole === 'passenger' && ride.car_model && (
                  <div className="text-sm text-muted-foreground">
                    Vehicle: {ride.car_model} - {ride.car_color}
                  </div>
                )}
              </div>

              <div className="flex flex-col items-end gap-2">
                <Badge variant={getStatusColor(ride.status)}>
                  {ride.status}
                </Badge>

                {ride.status === 'completed' && ride.can_rate && !ride.rating_given && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onRateUser(ride)}
                  >
                    <Star className="w-4 h-4 mr-1" />
                    Rate {userRole === 'driver' ? 'Passenger' : 'Driver'}
                  </Button>
                )}

                {ride.rating_given && (
                  <span className="text-xs text-muted-foreground">Rating given</span>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default RideHistoryPage;
