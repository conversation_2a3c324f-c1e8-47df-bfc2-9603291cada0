from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Ride, RideBooking, RideRequest, RideWaypoint, RidePending
from accounts.serializers import UserSerializer

User = get_user_model()


class RideWaypointSerializer(serializers.ModelSerializer):
    """Serializer for ride waypoints"""

    class Meta:
        model = RideWaypoint
        fields = '__all__'
        read_only_fields = ['ride']


class RideSerializer(serializers.ModelSerializer):
    """Serializer for ride information"""

    driver = UserSerializer(read_only=True)
    waypoints = RideWaypointSerializer(many=True, read_only=True)
    seats_booked = serializers.ReadOnlyField()
    seats_remaining = serializers.ReadOnlyField()

    class Meta:
        model = Ride
        fields = '__all__'
        read_only_fields = ['driver', 'created_at', 'updated_at']

    def create(self, validated_data):
        validated_data['driver'] = self.context['request'].user
        return super().create(validated_data)


class RideCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating rides"""

    waypoints = RideWaypointSerializer(many=True, required=False)

    class Meta:
        model = Ride
        fields = [
            'origin', 'destination', 'departure_time', 'arrival_time',
            'price', 'distance', 'seats_available', 'total_seats',
            'vehicle', 'car_model', 'car_color', 'features', 'notes', 'waypoints'
        ]

    def validate_vehicle(self, value):
        """Validate that the vehicle belongs to the current user"""
        if value and value.driver.user != self.context['request'].user:
            raise serializers.ValidationError("You can only use your own vehicles")
        return value

    def create(self, validated_data):
        waypoints_data = validated_data.pop('waypoints', [])
        # Don't override driver if it's already set by the view
        if 'driver' not in validated_data:
            validated_data['driver'] = self.context['request'].user

        # If vehicle is selected, auto-fill car details
        vehicle = validated_data.get('vehicle')
        if vehicle:
            validated_data['car_model'] = vehicle.full_name
            validated_data['car_color'] = vehicle.color
            validated_data['seats_available'] = min(
                validated_data.get('seats_available', vehicle.seats),
                vehicle.seats
            )
            validated_data['total_seats'] = vehicle.seats
            # Add vehicle features to ride features
            vehicle_features = vehicle.features_list
            ride_features = validated_data.get('features', [])
            validated_data['features'] = list(set(ride_features + vehicle_features))

        ride = Ride.objects.create(**validated_data)

        # Create waypoints
        for waypoint_data in waypoints_data:
            RideWaypoint.objects.create(ride=ride, **waypoint_data)

        return ride


class RideListSerializer(serializers.ModelSerializer):
    """Simplified serializer for ride listings"""

    driver_name = serializers.CharField(source='driver.full_name', read_only=True)
    driver_rating = serializers.DecimalField(source='driver.rating', max_digits=3, decimal_places=2, read_only=True)
    driver_avatar = serializers.ImageField(source='driver.avatar', read_only=True)
    seats_remaining = serializers.ReadOnlyField()

    class Meta:
        model = Ride
        fields = [
            'id', 'origin', 'destination', 'departure_time', 'arrival_time',
            'price', 'distance', 'seats_available', 'seats_remaining',
            'car_model', 'car_color', 'features', 'driver_name',
            'driver_rating', 'driver_avatar', 'status'
        ]


class RideBookingSerializer(serializers.ModelSerializer):
    """Serializer for ride bookings"""

    ride = RideListSerializer(read_only=True)
    user = UserSerializer(read_only=True)
    amount_to_pay = serializers.SerializerMethodField()

    class Meta:
        model = RideBooking
        fields = '__all__'
        read_only_fields = ['user', 'created_at', 'updated_at']

    def get_amount_to_pay(self, obj):
        return obj.calculate_amount()


class RideBookingCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ride bookings"""

    class Meta:
        model = RideBooking
        fields = [
            'ride', 'seats_booked', 'pickup_location',
            'dropoff_location', 'special_requests'
        ]

    def validate(self, attrs):
        ride = attrs['ride']
        user = self.context['request'].user
        seats_requested = attrs.get('seats_booked', 1)

        # Check if user can book this ride
        can_book, message = ride.can_be_booked_by(user)
        if not can_book:
            raise serializers.ValidationError(message)

        # Check if enough seats are available
        if seats_requested > ride.seats_remaining:
            raise serializers.ValidationError(
                f"Only {ride.seats_remaining} seats available"
            )

        return attrs

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        validated_data['amount_paid'] = validated_data['ride'].price * validated_data.get('seats_booked', 1)
        return super().create(validated_data)


class RideRequestSerializer(serializers.ModelSerializer):
    """Serializer for ride requests"""

    passenger = UserSerializer(read_only=True)
    matched_ride = RideListSerializer(read_only=True)

    class Meta:
        model = RideRequest
        fields = '__all__'
        read_only_fields = ['passenger', 'matched_ride', 'created_at', 'updated_at']

    def create(self, validated_data):
        validated_data['passenger'] = self.context['request'].user
        return super().create(validated_data)


class RideSearchSerializer(serializers.Serializer):
    """Serializer for ride search parameters"""

    origin = serializers.CharField(required=False)
    destination = serializers.CharField(required=False)
    departure_date = serializers.DateField(required=False)
    departure_time_from = serializers.TimeField(required=False)
    departure_time_to = serializers.TimeField(required=False)
    max_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    min_seats = serializers.IntegerField(required=False)
    features = serializers.ListField(child=serializers.CharField(), required=False)

    # Location-based search
    origin_lat = serializers.DecimalField(max_digits=10, decimal_places=8, required=False)
    origin_lng = serializers.DecimalField(max_digits=11, decimal_places=8, required=False)
    destination_lat = serializers.DecimalField(max_digits=10, decimal_places=8, required=False)
    destination_lng = serializers.DecimalField(max_digits=11, decimal_places=8, required=False)
    radius_km = serializers.IntegerField(default=50, required=False)


class RideStatsSerializer(serializers.Serializer):
    """Serializer for ride statistics"""

    total_rides = serializers.IntegerField()
    active_rides = serializers.IntegerField()
    completed_rides = serializers.IntegerField()
    cancelled_rides = serializers.IntegerField()
    total_bookings = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)


class RidePendingSerializer(serializers.ModelSerializer):
    """Serializer for pending ride bookings"""

    ride = RideListSerializer(read_only=True)
    passenger = UserSerializer(read_only=True)

    class Meta:
        model = RidePending
        fields = '__all__'
        read_only_fields = ['passenger', 'status', 'responded_at', 'created_at', 'updated_at']


class RidePendingCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating pending ride bookings"""

    class Meta:
        model = RidePending
        fields = [
            'ride', 'seats_requested', 'pickup_location',
            'dropoff_location', 'passenger_name', 'passenger_phone', 'special_requests'
        ]

    def validate(self, attrs):
        ride = attrs['ride']
        user = self.context['request'].user
        seats_requested = attrs.get('seats_requested', 1)

        # Check if user is a passenger
        if user.role != 'passenger':
            raise serializers.ValidationError("Only passengers can book rides")

        # Check if user already has a pending booking for this ride
        if RidePending.objects.filter(ride=ride, passenger=user, status='pending').exists():
            raise serializers.ValidationError("You already have a pending booking for this ride")

        # Check if user already has a confirmed booking for this ride
        if RideBooking.objects.filter(ride=ride, user=user, status='confirmed').exists():
            raise serializers.ValidationError("You already have a confirmed booking for this ride")

        # Check if enough seats are potentially available
        if seats_requested > ride.seats_available:
            raise serializers.ValidationError(
                f"Only {ride.seats_available} seats available"
            )

        return attrs

    def create(self, validated_data):
        validated_data['passenger'] = self.context['request'].user
        return super().create(validated_data)


class RidePendingResponseSerializer(serializers.Serializer):
    """Serializer for driver response to pending bookings"""

    action = serializers.ChoiceField(choices=['confirm', 'reject'])
    driver_response = serializers.CharField(required=False, allow_blank=True)
