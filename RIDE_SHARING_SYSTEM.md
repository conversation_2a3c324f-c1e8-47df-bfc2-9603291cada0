# Comprehensive Ride-Sharing System

## Overview
This is a complete ride-sharing application built with Django (backend) and React (frontend) that supports both drivers and passengers with comprehensive profile management, vehicle management, ride booking, and rating systems.

## Features Implemented

### 🚗 User Management
- **Dual Role System**: Users can register as drivers, passengers, or both
- **Profile Management**: Complete profile editing with personal information
- **Authentication**: Secure login/logout with session management
- **Role-based Access**: Different dashboards and features based on user role

### 🚙 Vehicle Management (Drivers)
- **Add Vehicles**: Drivers can add multiple vehicles with detailed information
- **Vehicle Features**: Track AC, music, WiFi, GPS, phone charger, smoking policy
- **Vehicle Photos**: Support for front, side, and interior photos
- **Vehicle Verification**: Admin verification system for vehicles
- **Vehicle Selection**: Choose specific vehicle when offering rides

### 🛣️ Ride Management
- **Offer Rides**: Drivers can create ride offers with vehicle selection
- **Search Rides**: Passengers can search rides by origin, destination, date, seats
- **Ride Details**: Complete ride information with driver/vehicle details
- **Price Setting**: Drivers set their own prices per seat
- **Route Management**: Origin, destination, waypoints support

### 📋 Booking System
- **Pending Requests**: All booking requests go to pending table first
- **Driver Approval**: Drivers can accept or decline booking requests
- **Booking Confirmation**: Confirmed bookings move to separate table
- **Booking Cancellation**: Both parties can cancel bookings
- **Real-time Status**: Track booking status (pending, confirmed, cancelled, completed)

### ⭐ Rating System
- **Mutual Rating**: Both drivers and passengers can rate each other
- **Detailed Categories**: Rate punctuality, communication, cleanliness, safety
- **Anonymous Option**: Option to submit ratings anonymously
- **Rating History**: View all given and received ratings
- **Average Calculation**: Automatic calculation of user average ratings

### 📊 Dashboard System
- **Driver Dashboard**: 
  - Total earnings tracking
  - Rides offered statistics
  - Active rides count
  - Pending booking requests
  - Vehicle management
  - Ride history access

- **Passenger Dashboard**:
  - Total spending tracking
  - Rides booked statistics
  - Active bookings count
  - Pending requests status
  - Booking history access

### 📱 User Interface
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Dark/Light Mode**: Theme switching support
- **Multi-language**: Arabic and English support with RTL
- **Modern UI**: Built with Tailwind CSS and shadcn/ui components

## Technical Architecture

### Backend (Django)
```
backend/
├── accounts/           # User management, profiles, vehicles, ratings
├── rides/             # Ride management, bookings, pending requests
├── cojourneyhub/      # Main Django settings
└── manage.py
```

### Frontend (React)
```
src/
├── components/        # Reusable UI components
├── pages/            # Main application pages
├── services/         # API service layers
├── hooks/            # Custom React hooks
├── contexts/         # React context providers
└── types/            # TypeScript type definitions
```

## Database Schema

### Key Models
1. **User**: Extended user model with role, rating, statistics
2. **Driver**: Driver-specific profile information
3. **Passenger**: Passenger-specific profile information
4. **Vehicle**: Vehicle information with features and verification
5. **Ride**: Ride offers with vehicle selection
6. **RidePending**: Pending booking requests
7. **RideBooking**: Confirmed bookings
8. **RideRating**: Rating and review system

## API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login
- `POST /api/auth/logout/` - User logout
- `GET /api/auth/profile/` - Get user profile
- `PATCH /api/auth/profile/` - Update user profile

### Dashboards
- `GET /api/auth/driver/dashboard/` - Driver dashboard data
- `GET /api/auth/passenger/dashboard/` - Passenger dashboard data

### Vehicles
- `GET /api/auth/vehicles/` - List user vehicles
- `POST /api/auth/vehicles/` - Add new vehicle
- `GET /api/auth/vehicles/{id}/` - Get vehicle details
- `PATCH /api/auth/vehicles/{id}/` - Update vehicle
- `DELETE /api/auth/vehicles/{id}/` - Delete vehicle

### Rides
- `GET /api/rides/` - List all rides
- `POST /api/rides/` - Create new ride
- `GET /api/rides/{id}/` - Get ride details
- `POST /api/rides/search/` - Search rides
- `GET /api/rides/my-rides/` - Get user's rides

### Bookings
- `GET /api/rides/pending/` - List pending bookings
- `POST /api/rides/{id}/request-booking/` - Request booking
- `POST /api/rides/pending/{id}/respond/` - Respond to booking
- `GET /api/rides/bookings/` - List confirmed bookings
- `POST /api/rides/bookings/{id}/cancel/` - Cancel booking

### Ride History
- `GET /api/rides/driver/history/` - Driver ride history
- `GET /api/rides/passenger/history/` - Passenger ride history

### Ratings
- `GET /api/auth/ratings/` - List user ratings
- `POST /api/auth/ratings/` - Submit rating
- `GET /api/auth/users/{id}/ratings/` - Get user's received ratings

## Setup Instructions

### Backend Setup
1. Navigate to backend directory: `cd backend`
2. Install dependencies: `pip install -r requirements.txt`
3. Run migrations: `python manage.py migrate`
4. Create superuser: `python manage.py createsuperuser`
5. Start server: `python manage.py runserver`

### Frontend Setup
1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. Access application: `http://localhost:8081`

## Usage Flow

### For Drivers
1. **Register** as driver or update role to driver
2. **Add Vehicle** through profile management or add vehicle page
3. **Offer Ride** by selecting vehicle and setting details
4. **Manage Requests** through driver dashboard
5. **Accept/Decline** booking requests
6. **Complete Rides** and rate passengers

### For Passengers
1. **Register** as passenger
2. **Search Rides** using search filters
3. **Request Booking** for desired rides
4. **Wait for Approval** from driver
5. **Complete Rides** and rate drivers

## Key Features in Detail

### Pending Booking System
- All booking requests go to `RidePending` table first
- Drivers see pending requests in their dashboard
- Drivers can accept (moves to `RideBooking`) or decline
- Real-time status updates for passengers

### Vehicle Integration
- Drivers select specific vehicle when offering rides
- Vehicle features automatically added to ride features
- Seat count validation based on vehicle capacity
- Vehicle verification status affects ride visibility

### Rating System
- Post-ride rating for both parties
- Multiple rating categories (punctuality, communication, etc.)
- Anonymous rating option
- Automatic average calculation and display

### Dashboard Analytics
- Real-time statistics for both user types
- Earnings/spending tracking
- Ride count and completion rates
- Pending request management

## Testing
Access the test page at `/test` to verify all API endpoints and functionality.

## Security Features
- CSRF protection
- Session-based authentication
- Role-based access control
- Input validation and sanitization
- SQL injection prevention

## Future Enhancements
- Real-time notifications
- Payment integration
- GPS tracking
- Chat system
- Advanced search filters
- Mobile app development

This system provides a complete, production-ready ride-sharing platform with all essential features for both drivers and passengers.
