import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useAuth } from "@/hooks/use-auth";
import { Car, Plus, DollarSign, Users, Clock, Star } from "lucide-react";

interface DriverDashboardData {
  driver_info: any;
  vehicles: any[];
  active_rides: number;
  pending_bookings: number;
  total_earnings: number;
  total_rides_offered: number;
}

interface PendingBooking {
  id: string;
  ride: {
    id: string;
    origin: string;
    destination: string;
    departure_time: string;
    price: number;
  };
  passenger: {
    id: string;
    full_name: string;
    phone_number: string;
    rating: number;
  };
  seats_requested: number;
  passenger_name: string;
  passenger_phone: string;
  special_requests: string;
  created_at: string;
}

const DriverDashboardPage = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState<DriverDashboardData | null>(null);
  const [pendingBookings, setPendingBookings] = useState<PendingBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user?.role === 'driver') {
      fetchDashboardData();
      fetchPendingBookings();
    }
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/auth/driver/dashboard/', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setDashboardData(data);
      } else {
        toast.error("Failed to load dashboard data");
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      toast.error("Failed to load dashboard data");
    }
  };

  const fetchPendingBookings = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/rides/pending/', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const data = await response.json();
        setPendingBookings(data.filter((booking: any) => booking.status === 'pending'));
      }
    } catch (error) {
      console.error("Error fetching pending bookings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBookingResponse = async (bookingId: string, action: 'confirm' | 'reject', response?: string) => {
    try {
      const res = await fetch(`http://localhost:8000/api/rides/pending/${bookingId}/respond/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action,
          driver_response: response || '',
        }),
      });

      if (res.ok) {
        toast.success(`Booking ${action}ed successfully`);
        fetchPendingBookings();
        fetchDashboardData();
      } else {
        toast.error(`Failed to ${action} booking`);
      }
    } catch (error) {
      console.error(`Error ${action}ing booking:`, error);
      toast.error(`Failed to ${action} booking`);
    }
  };

  if (user?.role !== 'driver') {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container mx-auto py-10 flex-1">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle>Access Denied</CardTitle>
              <CardDescription>
                Only drivers can access the driver dashboard.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className="w-full">
                <Link to="/">Go Home</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container mx-auto py-10 flex-1">
          <div className="text-center">Loading dashboard...</div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container mx-auto py-10 flex-1">
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold">Driver Dashboard</h1>
            <div className="flex gap-2">
              <Button asChild>
                <Link to="/add-vehicle">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Vehicle
                </Link>
              </Button>
              <Button asChild>
                <Link to="/offer-ride">
                  <Plus className="w-4 h-4 mr-2" />
                  Offer Ride
                </Link>
              </Button>
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.total_earnings?.toFixed(2) || '0.00'} TND</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Rides Offered</CardTitle>
                <Car className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.total_rides_offered || 0}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Rides</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.active_rides || 0}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Requests</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.pending_bookings || 0}</div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="pending" className="space-y-4">
            <TabsList>
              <TabsTrigger value="pending">Pending Requests</TabsTrigger>
              <TabsTrigger value="vehicles">My Vehicles</TabsTrigger>
              <TabsTrigger value="rides">My Rides</TabsTrigger>
            </TabsList>

            <TabsContent value="pending" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Pending Booking Requests</CardTitle>
                  <CardDescription>
                    Review and respond to passenger booking requests
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {pendingBookings.length === 0 ? (
                    <p className="text-muted-foreground text-center py-8">
                      No pending booking requests
                    </p>
                  ) : (
                    <div className="space-y-4">
                      {pendingBookings.map((booking) => (
                        <Card key={booking.id}>
                          <CardContent className="pt-6">
                            <div className="flex justify-between items-start">
                              <div className="space-y-2">
                                <h4 className="font-semibold">
                                  {booking.ride.origin} → {booking.ride.destination}
                                </h4>
                                <p className="text-sm text-muted-foreground">
                                  Passenger: {booking.passenger_name}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  Phone: {booking.passenger_phone}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  Seats: {booking.seats_requested}
                                </p>
                                {booking.special_requests && (
                                  <p className="text-sm text-muted-foreground">
                                    Special requests: {booking.special_requests}
                                  </p>
                                )}
                                <div className="flex items-center gap-2">
                                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                                  <span className="text-sm">{booking.passenger.rating}</span>
                                </div>
                              </div>
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  onClick={() => handleBookingResponse(booking.id, 'confirm')}
                                >
                                  Accept
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleBookingResponse(booking.id, 'reject')}
                                >
                                  Decline
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="vehicles" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>My Vehicles</CardTitle>
                  <CardDescription>
                    Manage your registered vehicles
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {!dashboardData?.vehicles || dashboardData.vehicles.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground mb-4">No vehicles registered</p>
                      <Button asChild>
                        <Link to="/add-vehicle">
                          <Plus className="w-4 h-4 mr-2" />
                          Add Your First Vehicle
                        </Link>
                      </Button>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {dashboardData.vehicles.map((vehicle) => (
                        <Card key={vehicle.id}>
                          <CardContent className="pt-6">
                            <div className="space-y-2">
                              <h4 className="font-semibold">{vehicle.full_name}</h4>
                              <p className="text-sm text-muted-foreground">
                                {vehicle.color} • {vehicle.seats} seats
                              </p>
                              <p className="text-sm text-muted-foreground">
                                License: {vehicle.license_plate}
                              </p>
                              <div className="flex flex-wrap gap-1">
                                {vehicle.features_list?.map((feature: string) => (
                                  <Badge key={feature} variant="secondary" className="text-xs">
                                    {feature}
                                  </Badge>
                                ))}
                              </div>
                              <Badge variant={vehicle.is_active ? "default" : "secondary"}>
                                {vehicle.is_active ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="rides" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>My Rides</CardTitle>
                  <CardDescription>
                    View and manage your ride offers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 space-y-4">
                    <div className="flex gap-4 justify-center">
                      <Button asChild>
                        <Link to="/offer-ride">
                          <Plus className="w-4 h-4 mr-2" />
                          Offer a Ride
                        </Link>
                      </Button>
                      <Button asChild variant="outline">
                        <Link to="/ride-history">
                          <Clock className="w-4 h-4 mr-2" />
                          View Ride History
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default DriverDashboardPage;
