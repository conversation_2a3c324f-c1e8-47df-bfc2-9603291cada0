import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useAuth } from "@/hooks/use-auth";
import { bookingService } from "@/services/bookingService";
import { vehicleService } from "@/services/vehicleService";

const TestPage = () => {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    try {
      const result = await testFn();
      setTestResults(prev => [...prev, { name: testName, status: 'success', result }]);
      return result;
    } catch (error: any) {
      setTestResults(prev => [...prev, { name: testName, status: 'error', error: error.message }]);
      throw error;
    }
  };

  const runAllTests = async () => {
    if (!user) {
      toast.error("Please login to run tests");
      return;
    }

    setIsRunning(true);
    setTestResults([]);

    try {
      // Test 1: Dashboard Data
      await runTest("Driver Dashboard", async () => {
        const response = await fetch('http://localhost:8000/api/auth/driver/dashboard/', {
          credentials: 'include',
        });
        if (!response.ok) throw new Error('Dashboard API failed');
        return await response.json();
      });

      // Test 2: Vehicle Service
      await runTest("Vehicle Service", async () => {
        return await vehicleService.getUserVehicles();
      });

      // Test 3: Booking Service
      await runTest("Pending Bookings", async () => {
        return await bookingService.getPendingBookings();
      });

      // Test 4: Ride Search
      await runTest("Ride Search", async () => {
        return await bookingService.searchRides({
          origin: "Tunis",
          destination: "Sousse",
          departure_date: new Date().toISOString().split('T')[0],
          min_seats: 1,
        });
      });

      // Test 5: User Rides
      await runTest("User Rides", async () => {
        return await bookingService.getUserRides();
      });

      // Test 6: Ratings API
      await runTest("Ratings API", async () => {
        const response = await fetch('http://localhost:8000/api/auth/ratings/', {
          credentials: 'include',
        });
        if (!response.ok) throw new Error('Ratings API failed');
        return await response.json();
      });

      toast.success("All tests completed!");
    } catch (error) {
      console.error("Test failed:", error);
      toast.error("Some tests failed");
    } finally {
      setIsRunning(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container mx-auto py-10 flex-1">
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold">System Test Page</h1>
            <div className="flex gap-2">
              <Button onClick={clearResults} variant="outline">
                Clear Results
              </Button>
              <Button onClick={runAllTests} disabled={isRunning}>
                {isRunning ? "Running Tests..." : "Run All Tests"}
              </Button>
            </div>
          </div>

          {!user && (
            <Card>
              <CardContent className="py-6">
                <p className="text-center text-muted-foreground">
                  Please login to run system tests
                </p>
              </CardContent>
            </Card>
          )}

          {user && (
            <Card>
              <CardHeader>
                <CardTitle>User Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <strong>Name:</strong> {user.fullName || user.name || 'N/A'}
                  </div>
                  <div>
                    <strong>Email:</strong> {user.email}
                  </div>
                  <div>
                    <strong>Role:</strong> {user.role}
                  </div>
                  <div>
                    <strong>ID:</strong> {user.id}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {testResults.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Test Results</CardTitle>
                <CardDescription>
                  Results from the latest test run
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {testResults.map((test, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-semibold">{test.name}</h4>
                        <Badge variant={test.status === 'success' ? 'default' : 'destructive'}>
                          {test.status}
                        </Badge>
                      </div>
                      
                      {test.status === 'success' && test.result && (
                        <div className="bg-green-50 p-3 rounded text-sm">
                          <strong>Result:</strong>
                          <pre className="mt-1 overflow-auto">
                            {JSON.stringify(test.result, null, 2)}
                          </pre>
                        </div>
                      )}
                      
                      {test.status === 'error' && (
                        <div className="bg-red-50 p-3 rounded text-sm text-red-700">
                          <strong>Error:</strong> {test.error}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>API Endpoints</CardTitle>
              <CardDescription>
                Available API endpoints for testing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Authentication</h4>
                  <ul className="text-sm space-y-1">
                    <li>• POST /api/auth/register/</li>
                    <li>• POST /api/auth/login/</li>
                    <li>• POST /api/auth/logout/</li>
                    <li>• GET /api/auth/profile/</li>
                    <li>• GET /api/auth/driver/dashboard/</li>
                    <li>• GET /api/auth/passenger/dashboard/</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Vehicles</h4>
                  <ul className="text-sm space-y-1">
                    <li>• GET /api/auth/vehicles/</li>
                    <li>• POST /api/auth/vehicles/</li>
                    <li>• PUT /api/auth/vehicles/:id/</li>
                    <li>• DELETE /api/auth/vehicles/:id/</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Rides</h4>
                  <ul className="text-sm space-y-1">
                    <li>• GET /api/rides/</li>
                    <li>• POST /api/rides/</li>
                    <li>• POST /api/rides/search/</li>
                    <li>• GET /api/rides/my-rides/</li>
                    <li>• GET /api/rides/driver/history/</li>
                    <li>• GET /api/rides/passenger/history/</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Bookings</h4>
                  <ul className="text-sm space-y-1">
                    <li>• GET /api/rides/pending/</li>
                    <li>• POST /api/rides/:id/request-booking/</li>
                    <li>• POST /api/rides/pending/:id/respond/</li>
                    <li>• GET /api/rides/bookings/</li>
                    <li>• POST /api/rides/bookings/:id/cancel/</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Ratings</h4>
                  <ul className="text-sm space-y-1">
                    <li>• GET /api/auth/ratings/</li>
                    <li>• POST /api/auth/ratings/</li>
                    <li>• GET /api/auth/users/:id/ratings/</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Feature Checklist</CardTitle>
              <CardDescription>
                Implemented features in the ride-sharing system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">✅ User Management</h4>
                  <ul className="text-sm space-y-1">
                    <li>• User registration with role selection</li>
                    <li>• Driver and passenger profiles</li>
                    <li>• Profile management and editing</li>
                    <li>• User authentication and authorization</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">✅ Vehicle Management</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Add/edit/delete vehicles</li>
                    <li>• Vehicle verification system</li>
                    <li>• Vehicle features and photos</li>
                    <li>• Vehicle selection in ride offers</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">✅ Ride Management</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Create ride offers</li>
                    <li>• Search and filter rides</li>
                    <li>• Ride booking system</li>
                    <li>• Pending booking requests</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">✅ Booking System</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Pending booking table</li>
                    <li>• Driver approval/rejection</li>
                    <li>• Booking confirmation</li>
                    <li>• Booking cancellation</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">✅ Rating System</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Rate drivers and passengers</li>
                    <li>• Detailed rating categories</li>
                    <li>• Anonymous rating option</li>
                    <li>• Rating history and display</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">✅ Dashboard System</h4>
                  <ul className="text-sm space-y-1">
                    <li>• Driver dashboard with statistics</li>
                    <li>• Passenger dashboard</li>
                    <li>• Ride history tracking</li>
                    <li>• Earnings and spending tracking</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default TestPage;
