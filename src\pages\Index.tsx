
import { useState, useEffect } from "react";
import { useSearchParams, useLocation } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Hero from "@/components/Hero";
import SearchBar, { SearchParams } from "@/components/SearchBar";
import RideList from "@/components/RideList";
import Footer from "@/components/Footer";
import Map from "@/components/Map";
import SystemStatus from "@/components/SystemStatus";

import { Ride } from "@/data/rides";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUser } from "@/contexts/UserContext";
import { rideService, RideSearchParams } from "@/services/rideService";
import { Button } from "@/components/ui/button";
import { MapIcon, List } from "lucide-react";

const Index = () => {
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const [filteredRides, setFilteredRides] = useState<Ride[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showMap, setShowMap] = useState(false);
  const [currentSearchParams, setCurrentSearchParams] = useState<SearchParams | null>(null);
  const { language } = useLanguage();
  const { isAuthenticated, pendingRideRequest } = useUser();

  // Load initial rides or from navigation state
  useEffect(() => {
    const loadInitialRides = async () => {
      setIsLoading(true);

      try {
        // Check if we have rides from navigation state (from search)
        if (location.state?.rides) {
          setFilteredRides(location.state.rides);
          setCurrentSearchParams(location.state.searchParams);
        } else {
          // Check URL parameters
          const origin = searchParams.get("origin");
          const destination = searchParams.get("destination");
          const date = searchParams.get("date");
          const passengers = searchParams.get("passengers");

          if (origin || destination || date) {
            const searchData: SearchParams = {
              origin: origin || "",
              destination: destination || "",
              date: date || "",
              passengers: parseInt(passengers || "1")
            };
            await filterRides(searchData);
          } else {
            // Load some default rides
            const defaultRides = await rideService.searchRides({});
            setFilteredRides(defaultRides);
          }
        }
      } catch (error) {
        console.error('Error loading rides:', error);
        setFilteredRides([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialRides();
  }, [searchParams, location.state]);

  useEffect(() => {
    // Apply pending request if available and authenticated
    if (isAuthenticated && pendingRideRequest) {
      filterRides(pendingRideRequest);
    }
  }, [isAuthenticated, pendingRideRequest]);

  const filterRides = async (params: SearchParams) => {
    setIsLoading(true);
    setCurrentSearchParams(params);

    try {
      // If all search parameters are empty, load all available rides
      const hasSearchCriteria = params.origin || params.destination || params.date;

      if (!hasSearchCriteria) {
        const allRides = await rideService.searchRides({});
        setFilteredRides(allRides);
      } else {
        const rideSearchParams: RideSearchParams = {
          origin: params.origin,
          destination: params.destination,
          departureDate: params.date,
          passengers: params.passengers
        };

        const rides = await rideService.searchRides(rideSearchParams);
        setFilteredRides(rides);
      }
    } catch (error) {
      console.error('Error filtering rides:', error);
      setFilteredRides([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Generate map markers from current search and rides
  const getMapMarkers = () => {
    const markers: Array<{
      position: [number, number];
      popup?: string;
      type?: 'origin' | 'destination' | 'waypoint';
    }> = [];

    // Add origin and destination from current search
    if (currentSearchParams?.originCoords) {
      markers.push({
        position: [currentSearchParams.originCoords.lat, currentSearchParams.originCoords.lng],
        popup: `Origin: ${currentSearchParams.origin}`,
        type: 'origin'
      });
    }

    if (currentSearchParams?.destinationCoords) {
      markers.push({
        position: [currentSearchParams.destinationCoords.lat, currentSearchParams.destinationCoords.lng],
        popup: `Destination: ${currentSearchParams.destination}`,
        type: 'destination'
      });
    }

    return markers;
  };

  const getMapRoute = () => {
    if (currentSearchParams?.originCoords && currentSearchParams?.destinationCoords) {
      return [
        [currentSearchParams.originCoords.lat, currentSearchParams.originCoords.lng] as [number, number],
        [currentSearchParams.destinationCoords.lat, currentSearchParams.destinationCoords.lng] as [number, number]
      ];
    }
    return [];
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-1">
        <Hero />



        <div className="container mx-auto px-4 -mt-8 relative z-10 mb-16">
          <div className="max-w-5xl mx-auto">
            <SearchBar onSearch={filterRides} />

            {/* System Status - only show for development */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-8">
                <SystemStatus />
              </div>
            )}

            <div className="mt-12">
              {/* Toggle between map and list view */}
              {filteredRides.length > 0 && (
                <div className="flex justify-end mb-4">
                  <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
                    <Button
                      variant={!showMap ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setShowMap(false)}
                      className="flex items-center gap-2"
                    >
                      <List className="h-4 w-4" />
                      List
                    </Button>
                    <Button
                      variant={showMap ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setShowMap(true)}
                      className="flex items-center gap-2"
                    >
                      <MapIcon className="h-4 w-4" />
                      Map
                    </Button>
                  </div>
                </div>
              )}

              {showMap && (currentSearchParams?.originCoords || currentSearchParams?.destinationCoords) ? (
                <div className="mb-8">
                  <Map
                    center={currentSearchParams?.originCoords ?
                      [currentSearchParams.originCoords.lat, currentSearchParams.originCoords.lng] :
                      [36.8065, 10.1815]
                    }
                    zoom={8}
                    markers={getMapMarkers()}
                    route={getMapRoute()}
                    height="400px"
                    className="rounded-lg shadow-lg"
                  />
                </div>
              ) : null}

              <RideList rides={filteredRides} />

              {isLoading && (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Index;
