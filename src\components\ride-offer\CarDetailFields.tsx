
import { useState, useEffect } from "react";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Car } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { useAuth } from "@/hooks/use-auth";
import { Control } from "react-hook-form";
import { vehicleService } from "@/services/vehicleService";
import { Vehicle } from "@/types/user";

interface CarDetailFieldsProps {
  control: Control<any>;
  isRTL: boolean;
  onVehicleSelect?: (vehicle: Vehicle | null) => void;
}

const CarDetailFields = ({ control, isRTL, onVehicleSelect }: CarDetailFieldsProps) => {
  const { translations } = useLanguage();
  const { user } = useAuth();
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [selectedVehicle, setSelectedVehicle] = useState<string>("");
  const [isLoadingVehicles, setIsLoadingVehicles] = useState(false);

  useEffect(() => {
    if (user?.role === 'driver') {
      fetchVehicles();
    }
  }, [user]);

  const fetchVehicles = async () => {
    setIsLoadingVehicles(true);
    try {
      const userVehicles = await vehicleService.getUserVehicles();
      setVehicles(userVehicles.filter(v => v.isActive));
    } catch (error) {
      console.error("Error fetching vehicles:", error);
      // Fallback to mock data for development
      setVehicles(vehicleService.getMockVehicles());
    } finally {
      setIsLoadingVehicles(false);
    }
  };

  const handleVehicleChange = (vehicleId: string, setValue: any) => {
    setSelectedVehicle(vehicleId);

    if (vehicleId === "manual") {
      // Manual entry
      setValue("carModel", "");
      setValue("carColor", "");
      onVehicleSelect?.(null);
    } else {
      // Selected vehicle
      const vehicle = vehicles.find(v => v.id === vehicleId);
      if (vehicle) {
        setValue("carModel", vehicleService.getVehicleDisplayName(vehicle));
        setValue("carColor", vehicle.color);
        onVehicleSelect?.(vehicle);
      }
    }
  };

  if (user?.role !== 'driver') {
    return (
      <>
        <FormField
          control={control}
          name="carModel"
          render={({ field }) => (
            <FormItem className={isRTL ? 'text-right' : ''}>
              <FormLabel className="dark:text-white">{translations.carModel}</FormLabel>
              <div className="relative">
                <Car className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-3 h-4 w-4 text-gray-500`} />
                <FormControl>
                  <Input placeholder="e.g. Volkswagen Polo" className={isRTL ? 'pr-10 text-right' : 'pl-10'} {...field} />
                </FormControl>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="carColor"
          render={({ field }) => (
            <FormItem className={isRTL ? 'text-right' : ''}>
              <FormLabel className="dark:text-white">{translations.carColor}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. White" className={isRTL ? 'text-right' : ''} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </>
    );
  }

  return (
    <>
      <FormField
        control={control}
        name="vehicle"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">Select Vehicle</FormLabel>
            <Select
              value={selectedVehicle}
              onValueChange={(value) => handleVehicleChange(value, field.onChange)}
              disabled={isLoadingVehicles}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder={isLoadingVehicles ? "Loading vehicles..." : "Choose your vehicle"} />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="manual">Enter manually</SelectItem>
                {vehicles.map((vehicle) => (
                  <SelectItem key={vehicle.id} value={vehicle.id}>
                    {vehicleService.getVehicleDisplayName(vehicle)} - {vehicle.color}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="carModel"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">{translations.carModel}</FormLabel>
            <div className="relative">
              <Car className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-3 h-4 w-4 text-gray-500`} />
              <FormControl>
                <Input
                  placeholder="e.g. Volkswagen Polo"
                  className={isRTL ? 'pr-10 text-right' : 'pl-10'}
                  {...field}
                  disabled={selectedVehicle && selectedVehicle !== "manual"}
                />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="carColor"
        render={({ field }) => (
          <FormItem className={isRTL ? 'text-right' : ''}>
            <FormLabel className="dark:text-white">{translations.carColor}</FormLabel>
            <FormControl>
              <Input
                placeholder="e.g. White"
                className={isRTL ? 'text-right' : ''}
                {...field}
                disabled={selectedVehicle && selectedVehicle !== "manual"}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default CarDetailFields;
