import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { vehicleService, VehicleCreateData } from "@/services/vehicleService";
import { useAuth } from "@/hooks/use-auth";

const AddVehiclePage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<VehicleCreateData>({
    make: "",
    model: "",
    year: new Date().getFullYear(),
    color: "",
    license_plate: "",
    vehicle_type: "sedan",
    seats: 4,
    has_ac: true,
    has_music: true,
    is_smoking_allowed: false,
    has_wifi: false,
    is_active: true,
  });

  const vehicleTypes = [
    { value: "sedan", label: "Sedan" },
    { value: "hatchback", label: "Hatchback" },
    { value: "suv", label: "SUV" },
    { value: "coupe", label: "Coupe" },
    { value: "convertible", label: "Convertible" },
    { value: "wagon", label: "Wagon" },
    { value: "van", label: "Van" },
    { value: "pickup", label: "Pickup Truck" },
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 30 }, (_, i) => currentYear - i);

  const handleInputChange = (field: keyof VehicleCreateData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (user?.role !== 'driver') {
      toast.error("Only drivers can add vehicles");
      return;
    }

    setIsLoading(true);

    try {
      const validation = vehicleService.validateVehicleData(formData);
      if (!validation.isValid) {
        toast.error(validation.errors[0]);
        return;
      }

      await vehicleService.createVehicle(formData);
      toast.success("Vehicle added successfully!");
      navigate("/profile");
    } catch (error) {
      console.error("Error adding vehicle:", error);
      toast.error("Failed to add vehicle. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (user?.role !== 'driver') {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container mx-auto py-10 flex-1">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle>Access Denied</CardTitle>
              <CardDescription>
                Only drivers can add vehicles. Please register as a driver to access this feature.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => navigate("/")} className="w-full">
                Go Home
              </Button>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container mx-auto py-10 flex-1">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Add New Vehicle</CardTitle>
              <CardDescription>
                Add a vehicle to your profile to start offering rides
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="make">Make *</Label>
                    <Input
                      id="make"
                      value={formData.make}
                      onChange={(e) => handleInputChange('make', e.target.value)}
                      placeholder="e.g., Toyota, Peugeot"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="model">Model *</Label>
                    <Input
                      id="model"
                      value={formData.model}
                      onChange={(e) => handleInputChange('model', e.target.value)}
                      placeholder="e.g., Corolla, 308"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="year">Year *</Label>
                    <Select
                      value={formData.year.toString()}
                      onValueChange={(value) => handleInputChange('year', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {years.map(year => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="color">Color *</Label>
                    <Input
                      id="color"
                      value={formData.color}
                      onChange={(e) => handleInputChange('color', e.target.value)}
                      placeholder="e.g., White, Blue"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="seats">Seats *</Label>
                    <Select
                      value={formData.seats.toString()}
                      onValueChange={(value) => handleInputChange('seats', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[2, 3, 4, 5, 6, 7, 8].map(seat => (
                          <SelectItem key={seat} value={seat.toString()}>
                            {seat} seats
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="license_plate">License Plate *</Label>
                    <Input
                      id="license_plate"
                      value={formData.license_plate}
                      onChange={(e) => handleInputChange('license_plate', e.target.value)}
                      placeholder="e.g., 123 TUN 456"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="vehicle_type">Vehicle Type *</Label>
                    <Select
                      value={formData.vehicle_type}
                      onValueChange={(value) => handleInputChange('vehicle_type', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {vehicleTypes.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-4">
                  <Label className="text-base font-medium">Vehicle Features</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="has_ac"
                        checked={formData.has_ac}
                        onCheckedChange={(checked) => handleInputChange('has_ac', checked)}
                      />
                      <Label htmlFor="has_ac">Air Conditioning</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="has_music"
                        checked={formData.has_music}
                        onCheckedChange={(checked) => handleInputChange('has_music', checked)}
                      />
                      <Label htmlFor="has_music">Music System</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="has_wifi"
                        checked={formData.has_wifi}
                        onCheckedChange={(checked) => handleInputChange('has_wifi', checked)}
                      />
                      <Label htmlFor="has_wifi">WiFi</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="is_smoking_allowed"
                        checked={formData.is_smoking_allowed}
                        onCheckedChange={(checked) => handleInputChange('is_smoking_allowed', checked)}
                      />
                      <Label htmlFor="is_smoking_allowed">Smoking Allowed</Label>
                    </div>
                  </div>
                </div>

                <div className="flex gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate("/profile")}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="flex-1"
                  >
                    {isLoading ? "Adding Vehicle..." : "Add Vehicle"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default AddVehiclePage;
