
import { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Car } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUser } from "@/contexts/UserContext";
import { useAuth } from "@/hooks/use-auth";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import LocationFields from "./LocationFields";
import DepartureFields from "./DepartureFields";
import RideDetailFields from "./RideDetailFields";
import CarDetailFields from "./CarDetailFields";
import Map from "@/components/Map";
import type { PendingRideOffer, Vehicle } from "@/types/user";
import { rideService } from "@/services/rideService";

const RideOfferForm = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showMap, setShowMap] = useState(false);
  const [originCoords, setOriginCoords] = useState<{ lat: number; lng: number } | undefined>();
  const [destinationCoords, setDestinationCoords] = useState<{ lat: number; lng: number } | undefined>();
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const { translations, language } = useLanguage();
  const { isAuthenticated, pendingRideOffer, setPendingRideOffer, addLoyaltyPoints, user } = useUser();
  const { user: authUser } = useAuth();
  const isRTL = language === "ar";

  // Create dynamic form schema based on current language
  const formSchema = z.object({
    origin: z.string().min(2, { message: translations.pleaseEnterStartingPoint }),
    destination: z.string().min(2, { message: translations.pleaseEnterDestination }),
    departureDate: z.string().min(1, { message: translations.pleaseSelectTravelDate }),
    departureTime: z.string().min(1, { message: translations.departureTime + " " + translations.pleaseSelectTravelDate.toLowerCase() }),
    availableSeats: z.string().min(1, { message: translations.availableSeats + " " + translations.pleaseSelectTravelDate.toLowerCase() }),
    price: z.string().min(1, { message: translations.pricePerSeat + " " + translations.pleaseSelectTravelDate.toLowerCase() }),
    vehicle: z.string().optional(),
    carModel: z.string().min(1, { message: translations.carModel + " " + translations.pleaseSelectTravelDate.toLowerCase() }),
    carColor: z.string().min(1, { message: translations.carColor + " " + translations.pleaseSelectTravelDate.toLowerCase() }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      origin: pendingRideOffer?.origin || "",
      destination: pendingRideOffer?.destination || "",
      departureDate: pendingRideOffer?.departureDate || "",
      departureTime: pendingRideOffer?.departureTime || "",
      availableSeats: pendingRideOffer?.availableSeats || "3",
      price: pendingRideOffer?.price || "",
      vehicle: "",
      carModel: pendingRideOffer?.carModel || "",
      carColor: pendingRideOffer?.carColor || "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!isAuthenticated) {
      // Save form data and redirect to login - ensure all fields are provided
      const rideOffer: PendingRideOffer = {
        origin: values.origin,
        destination: values.destination,
        departureDate: values.departureDate,
        departureTime: values.departureTime,
        availableSeats: values.availableSeats,
        price: values.price,
        carModel: values.carModel,
        carColor: values.carColor
      };

      setPendingRideOffer(rideOffer);
      toast.info(translations.loginRequiredForOfferRide || "Please login to offer a ride");
      navigate("/login?redirect=offer-ride");
      return;
    }

    if (!user?.id) {
      toast.error("User information not available. Please try logging in again.");
      return;
    }

    setIsSubmitting(true);

    try {
      // Calculate arrival time (estimated as 30 min per 100km)
      const departureDateTime = new Date(`${values.departureDate}T${values.departureTime}`);
      const estimatedDistance = Math.floor(Math.random() * 200) + 50; // Random distance between 50-250km
      const travelTimeMinutes = Math.floor(estimatedDistance * 0.3); // 0.3 minutes per km
      const arrivalDateTime = new Date(departureDateTime.getTime() + travelTimeMinutes * 60000);

      // Create ride data
      const rideData = {
        origin: values.origin,
        destination: values.destination,
        departure_time: departureDateTime.toISOString(),
        arrival_time: arrivalDateTime.toISOString(),
        price: parseFloat(values.price),
        distance: `${estimatedDistance} km`,
        seats_available: parseInt(values.availableSeats),
        total_seats: selectedVehicle?.seats || parseInt(values.availableSeats),
        vehicle: selectedVehicle?.id || null,
        car_model: values.carModel,
        car_color: values.carColor,
        features: selectedVehicle?.features_list || ["Cash payment only"],
        origin_latitude: originCoords?.lat,
        origin_longitude: originCoords?.lng,
        destination_latitude: destinationCoords?.lat,
        destination_longitude: destinationCoords?.lng
      };

      // Create the ride using the service
      const newRide = await rideService.createRide(rideData, user.id);

      if (newRide) {
        // Add loyalty points for offering a ride
        addLoyaltyPoints(50);

        // Success notification
        toast.success(translations.ridePostedSuccessfully, {
          description: translations.loyaltyPointsAdded?.replace("{points}", "50") || "50 loyalty points added to your account!",
        });

        // Clear pending offer
        setPendingRideOffer(null);

        navigate("/");
      } else {
        toast.error("Failed to create ride. Please try again.");
      }
    } catch (error) {
      console.error('Error creating ride:', error);
      toast.error("Failed to create ride. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
      <div className={`flex items-center gap-2 mb-6 ${isRTL ? 'justify-end' : 'justify-start'}`}>
        <Car className="h-6 w-6 text-primary" />
        <h1 className={`text-2xl font-bold dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}>
          {translations.offerRideTitle}
        </h1>
      </div>

      <p className={`text-gray-500 dark:text-gray-400 mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
        {translations.offerRideDescription}
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <LocationFields
              control={form.control}
              isRTL={isRTL}
              onOriginChange={setOriginCoords}
              onDestinationChange={setDestinationCoords}
            />
            <DepartureFields control={form.control} isRTL={isRTL} />
            <RideDetailFields control={form.control} isRTL={isRTL} />
            <CarDetailFields
              control={form.control}
              isRTL={isRTL}
              onVehicleSelect={setSelectedVehicle}
            />
          </div>

          <div className={`pt-4 ${isRTL ? 'text-right' : 'text-left'}`}>
            <Button type="submit" className="w-full md:w-auto" disabled={isSubmitting}>
              {isSubmitting ? translations.publishing : translations.publishRide}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default RideOfferForm;
