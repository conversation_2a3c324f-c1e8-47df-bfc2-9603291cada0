from django.urls import path
from . import views

urlpatterns = [
    # Rides
    path('', views.RideListCreateView.as_view(), name='ride-list-create'),
    path('<int:pk>/', views.RideDetailView.as_view(), name='ride-detail'),
    path('search/', views.search_rides, name='ride-search'),
    path('my-rides/', views.UserRidesView.as_view(), name='user-rides'),
    path('nearby/', views.nearby_rides, name='nearby-rides'),
    path('stats/', views.ride_stats, name='ride-stats'),
    
    # Bookings
    path('bookings/', views.RideBookingListCreateView.as_view(), name='booking-list-create'),
    path('bookings/<int:pk>/', views.RideBookingDetailView.as_view(), name='booking-detail'),
    path('<int:ride_id>/book/', views.book_ride, name='book-ride'),
    path('bookings/<int:booking_id>/cancel/', views.cancel_booking, name='cancel-booking'),
    
    # Ride requests
    path('requests/', views.RideRequestListCreateView.as_view(), name='ride-request-list-create'),

    # Pending bookings
    path('pending/', views.RidePendingListCreateView.as_view(), name='pending-booking-list-create'),
    path('pending/<int:pending_id>/respond/', views.respond_to_pending_booking, name='respond-pending-booking'),
    path('<int:ride_id>/request-booking/', views.request_ride_booking, name='request-ride-booking'),

    # Ride history
    path('driver/history/', views.driver_ride_history, name='driver-ride-history'),
    path('passenger/history/', views.passenger_ride_history, name='passenger-ride-history'),
]
