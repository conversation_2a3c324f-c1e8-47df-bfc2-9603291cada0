import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react";

interface SystemCheck {
  name: string;
  status: 'success' | 'error' | 'warning' | 'pending';
  message: string;
  endpoint?: string;
}

const SystemStatus = () => {
  const [checks, setChecks] = useState<SystemCheck[]>([]);
  const [isChecking, setIsChecking] = useState(false);

  const systemChecks = [
    {
      name: "Backend Server",
      endpoint: "http://localhost:8000/api/auth/profile/",
      description: "Django backend server connectivity"
    },
    {
      name: "Authentication API",
      endpoint: "http://localhost:8000/api/auth/profile/",
      description: "User authentication system"
    },
    {
      name: "Rides API",
      endpoint: "http://localhost:8000/api/rides/",
      description: "Ride management system"
    },
    {
      name: "Vehicles API",
      endpoint: "http://localhost:8000/api/auth/vehicles/",
      description: "Vehicle management system"
    },
    {
      name: "Bookings API",
      endpoint: "http://localhost:8000/api/rides/pending/",
      description: "Booking and pending system"
    },
    {
      name: "Ratings API",
      endpoint: "http://localhost:8000/api/auth/ratings/",
      description: "Rating and review system"
    }
  ];

  const runSystemChecks = async () => {
    setIsChecking(true);
    const results: SystemCheck[] = [];

    for (const check of systemChecks) {
      try {
        const response = await fetch(check.endpoint, {
          method: 'GET',
          credentials: 'include',
        });

        if (response.ok || response.status === 401) {
          // 401 is expected for protected endpoints when not logged in
          results.push({
            name: check.name,
            status: 'success',
            message: `${check.description} - API responding correctly`,
            endpoint: check.endpoint
          });
        } else {
          results.push({
            name: check.name,
            status: 'warning',
            message: `${check.description} - API returned status ${response.status}`,
            endpoint: check.endpoint
          });
        }
      } catch (error: any) {
        results.push({
          name: check.name,
          status: 'error',
          message: `${check.description} - Connection failed: ${error.message}`,
          endpoint: check.endpoint
        });
      }
    }

    setChecks(results);
    setIsChecking(false);
  };

  useEffect(() => {
    runSystemChecks();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-blue-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">Online</Badge>;
      case 'error':
        return <Badge variant="destructive">Offline</Badge>;
      case 'warning':
        return <Badge variant="secondary">Warning</Badge>;
      case 'pending':
        return <Badge variant="outline">Checking</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const overallStatus = checks.length > 0 ? (
    checks.every(check => check.status === 'success') ? 'success' :
    checks.some(check => check.status === 'error') ? 'error' : 'warning'
  ) : 'pending';

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(overallStatus)}
              System Status
            </CardTitle>
            <CardDescription>
              Real-time status of all system components
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(overallStatus)}
            <Button 
              onClick={runSystemChecks} 
              disabled={isChecking}
              size="sm"
              variant="outline"
            >
              {isChecking ? "Checking..." : "Refresh"}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {checks.map((check, index) => (
            <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
              {getStatusIcon(check.status)}
              <div className="flex-1">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">{check.name}</h4>
                  {getStatusBadge(check.status)}
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {check.message}
                </p>
                {check.endpoint && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Endpoint: {check.endpoint}
                  </p>
                )}
              </div>
            </div>
          ))}

          {checks.length === 0 && isChecking && (
            <div className="text-center py-8">
              <Clock className="w-8 h-8 mx-auto mb-2 animate-spin" />
              <p className="text-muted-foreground">Running system checks...</p>
            </div>
          )}

          {checks.length === 0 && !isChecking && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No system checks run yet</p>
            </div>
          )}
        </div>

        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">System Information</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Frontend:</strong> React + TypeScript + Vite
            </div>
            <div>
              <strong>Backend:</strong> Django + Django REST Framework
            </div>
            <div>
              <strong>Database:</strong> SQLite (Development)
            </div>
            <div>
              <strong>Authentication:</strong> Session-based
            </div>
            <div>
              <strong>UI Framework:</strong> Tailwind CSS + shadcn/ui
            </div>
            <div>
              <strong>API Base:</strong> http://localhost:8000/api/
            </div>
          </div>
        </div>

        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium mb-2 text-blue-900">Quick Links</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <a href="/test" className="text-blue-600 hover:underline">• System Test Page</a>
            <a href="/driver/dashboard" className="text-blue-600 hover:underline">• Driver Dashboard</a>
            <a href="/passenger/dashboard" className="text-blue-600 hover:underline">• Passenger Dashboard</a>
            <a href="/add-vehicle" className="text-blue-600 hover:underline">• Add Vehicle</a>
            <a href="/offer-ride" className="text-blue-600 hover:underline">• Offer Ride</a>
            <a href="/search" className="text-blue-600 hover:underline">• Search Rides</a>
            <a href="/ride-history" className="text-blue-600 hover:underline">• Ride History</a>
            <a href="/profile" className="text-blue-600 hover:underline">• Profile Management</a>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SystemStatus;
