from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User, UserProfile, Vehicle, Driver, Passenger, RideRating


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration"""

    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = [
            'email', 'username', 'full_name', 'password', 'password_confirm',
            'phone_number', 'city', 'country', 'role'
        ]

    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")

        # Validate role is provided
        if 'role' not in attrs or not attrs['role']:
            raise serializers.ValidationError("Role must be selected")

        if attrs['role'] not in ['driver', 'passenger']:
            raise serializers.ValidationError("Role must be either 'driver' or 'passenger'")

        return attrs

    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        role = validated_data.get('role')

        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()

        # Create user profile
        UserProfile.objects.create(user=user)

        return user


class DriverRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for driver-specific registration data"""

    # User fields
    email = serializers.EmailField()
    username = serializers.CharField()
    full_name = serializers.CharField()
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    phone_number = serializers.CharField(required=False, allow_blank=True)
    city = serializers.CharField(required=False, allow_blank=True)
    country = serializers.CharField(default='Tunisia')

    # Driver-specific fields
    driver_license_number = serializers.CharField()
    license_expiry_date = serializers.DateField()

    # First vehicle fields
    vehicle_make = serializers.CharField()
    vehicle_model = serializers.CharField()
    vehicle_year = serializers.IntegerField()
    vehicle_color = serializers.CharField()
    vehicle_license_plate = serializers.CharField()
    vehicle_type = serializers.CharField(default='sedan')
    vehicle_seats = serializers.IntegerField(default=4)

    class Meta:
        model = Driver
        fields = [
            'email', 'username', 'full_name', 'password', 'password_confirm',
            'phone_number', 'city', 'country', 'driver_license_number',
            'license_expiry_date', 'vehicle_make', 'vehicle_model',
            'vehicle_year', 'vehicle_color', 'vehicle_license_plate',
            'vehicle_type', 'vehicle_seats'
        ]

    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs

    def create(self, validated_data):
        # Extract user data
        user_data = {
            'email': validated_data.pop('email'),
            'username': validated_data.pop('username'),
            'full_name': validated_data.pop('full_name'),
            'phone_number': validated_data.pop('phone_number', ''),
            'city': validated_data.pop('city', ''),
            'country': validated_data.pop('country', 'Tunisia'),
            'role': 'driver'
        }

        # Extract vehicle data
        vehicle_data = {
            'make': validated_data.pop('vehicle_make'),
            'model': validated_data.pop('vehicle_model'),
            'year': validated_data.pop('vehicle_year'),
            'color': validated_data.pop('vehicle_color'),
            'license_plate': validated_data.pop('vehicle_license_plate'),
            'vehicle_type': validated_data.pop('vehicle_type', 'sedan'),
            'seats': validated_data.pop('vehicle_seats', 4),
        }

        password = validated_data.pop('password')
        validated_data.pop('password_confirm')

        # Create user
        user = User.objects.create_user(**user_data)
        user.set_password(password)
        user.save()

        # Create user profile
        UserProfile.objects.create(user=user)

        # Create driver profile
        driver = Driver.objects.create(
            user=user,
            driver_license_number=validated_data['driver_license_number'],
            license_expiry_date=validated_data['license_expiry_date']
        )

        # Create first vehicle
        Vehicle.objects.create(driver=driver, **vehicle_data)

        return driver


class PassengerRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for passenger-specific registration data"""

    # User fields
    email = serializers.EmailField()
    username = serializers.CharField()
    full_name = serializers.CharField()
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    phone_number = serializers.CharField(required=False, allow_blank=True)
    city = serializers.CharField(required=False, allow_blank=True)
    country = serializers.CharField(default='Tunisia')

    # Passenger-specific fields
    id_card_number = serializers.CharField()

    class Meta:
        model = Passenger
        fields = [
            'email', 'username', 'full_name', 'password', 'password_confirm',
            'phone_number', 'city', 'country', 'id_card_number'
        ]

    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs

    def create(self, validated_data):
        # Extract user data
        user_data = {
            'email': validated_data.pop('email'),
            'username': validated_data.pop('username'),
            'full_name': validated_data.pop('full_name'),
            'phone_number': validated_data.pop('phone_number', ''),
            'city': validated_data.pop('city', ''),
            'country': validated_data.pop('country', 'Tunisia'),
            'role': 'passenger'
        }

        password = validated_data.pop('password')
        validated_data.pop('password_confirm')

        # Create user
        user = User.objects.create_user(**user_data)
        user.set_password(password)
        user.save()

        # Create user profile
        UserProfile.objects.create(user=user)

        # Create passenger profile
        passenger = Passenger.objects.create(
            user=user,
            id_card_number=validated_data['id_card_number']
        )

        return passenger


class UserLoginSerializer(serializers.Serializer):
    """Serializer for user login"""
    
    email = serializers.EmailField()
    password = serializers.CharField()
    
    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(username=email, password=password)
            if not user:
                raise serializers.ValidationError('Invalid credentials')
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('Must include email and password')
        
        return attrs


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile"""
    
    class Meta:
        model = UserProfile
        fields = '__all__'
        read_only_fields = ['user', 'created_at', 'updated_at']


class VehicleSerializer(serializers.ModelSerializer):
    """Serializer for vehicle information"""

    full_name = serializers.ReadOnlyField()
    features_list = serializers.ReadOnlyField()

    class Meta:
        model = Vehicle
        fields = '__all__'
        read_only_fields = ['driver', 'created_at', 'updated_at', 'verification_date']


class DriverSerializer(serializers.ModelSerializer):
    """Serializer for driver information"""

    # Use string reference to avoid circular import
    user = serializers.SerializerMethodField()
    vehicles = VehicleSerializer(many=True, read_only=True)

    class Meta:
        model = Driver
        fields = '__all__'
        read_only_fields = ['user', 'total_rides_offered', 'total_earnings', 'created_at', 'updated_at']

    def get_user(self, obj):
        # Import here to avoid circular import
        return {
            'id': obj.user.id,
            'email': obj.user.email,
            'full_name': obj.user.full_name,
            'phone_number': obj.user.phone_number,
            'city': obj.user.city,
            'country': obj.user.country,
            'rating': obj.user.rating,
            'total_ratings': obj.user.total_ratings,
            'is_verified': obj.user.is_verified,
        }


class PassengerSerializer(serializers.ModelSerializer):
    """Serializer for passenger information"""

    # Use string reference to avoid circular import
    user = serializers.SerializerMethodField()

    class Meta:
        model = Passenger
        fields = '__all__'
        read_only_fields = ['user', 'total_rides_booked', 'total_spent', 'created_at', 'updated_at']

    def get_user(self, obj):
        # Import here to avoid circular import
        return {
            'id': obj.user.id,
            'email': obj.user.email,
            'full_name': obj.user.full_name,
            'phone_number': obj.user.phone_number,
            'city': obj.user.city,
            'country': obj.user.country,
            'rating': obj.user.rating,
            'total_ratings': obj.user.total_ratings,
            'is_verified': obj.user.is_verified,
        }


class UserSerializer(serializers.ModelSerializer):
    """Serializer for user information"""
    
    profile = UserProfileSerializer(read_only=True)
    vehicles = VehicleSerializer(many=True, read_only=True)
    ride_count = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'full_name', 'phone_number',
            'date_of_birth', 'bio', 'avatar', 'city', 'country',
            'rides_offered', 'rides_completed', 'loyalty_points',
            'referrals', 'rating', 'total_ratings', 'is_verified',
            'email_notifications', 'sms_notifications', 'ride_count',
            'profile', 'vehicles', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'rides_offered', 'rides_completed', 'loyalty_points',
            'referrals', 'rating', 'total_ratings', 'is_verified',
            'ride_count', 'created_at', 'updated_at'
        ]


class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user information"""
    
    class Meta:
        model = User
        fields = [
            'full_name', 'phone_number', 'date_of_birth', 'bio',
            'avatar', 'city', 'country', 'email_notifications',
            'sms_notifications'
        ]


class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for changing password"""
    
    old_password = serializers.CharField()
    new_password = serializers.CharField(validators=[validate_password])
    new_password_confirm = serializers.CharField()
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match")
        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect")
        return value


class UserStatsSerializer(serializers.ModelSerializer):
    """Serializer for user statistics"""
    
    ride_count = serializers.ReadOnlyField()
    
    class Meta:
        model = User
        fields = [
            'rides_offered', 'rides_completed', 'loyalty_points',
            'referrals', 'rating', 'total_ratings', 'ride_count'
        ]


class RideRatingSerializer(serializers.ModelSerializer):
    """Serializer for ride ratings"""

    rater_name = serializers.CharField(source='rater.full_name', read_only=True)
    rated_user_name = serializers.CharField(source='rated_user.full_name', read_only=True)
    ride_info = serializers.SerializerMethodField()

    class Meta:
        model = RideRating
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

    def get_ride_info(self, obj):
        return {
            'id': obj.ride.id,
            'origin': obj.ride.origin,
            'destination': obj.ride.destination,
            'departure_time': obj.ride.departure_time,
        }


class RideRatingCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ride ratings"""

    class Meta:
        model = RideRating
        fields = [
            'ride', 'rated_user', 'rating', 'review',
            'punctuality', 'communication', 'cleanliness', 'safety',
            'is_anonymous'
        ]

    def create(self, validated_data):
        validated_data['rater'] = self.context['request'].user
        return super().create(validated_data)
