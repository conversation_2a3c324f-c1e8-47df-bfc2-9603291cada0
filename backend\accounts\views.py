from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth import authenticate, login, logout
from django.shortcuts import get_object_or_404
from django.db import models
from .models import User, UserProfile, Vehicle, Driver, Passenger, RideRating
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserSerializer,
    UserUpdateSerializer, PasswordChangeSerializer, UserStatsSerializer,
    UserProfileSerializer, VehicleSerializer, DriverRegistrationSerializer,
    PassengerRegistrationSerializer, DriverSerializer, PassengerSerializer,
    RideRatingSerializer, RideRatingCreateSerializer
)


class UserRegistrationView(generics.CreateAPIView):
    """User registration endpoint"""

    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Log the user in
        login(request, user)

        return Response({
            'user': UserSerializer(user).data,
            'message': 'User registered and logged in successfully'
        }, status=status.HTTP_201_CREATED)


class DriverRegistrationView(generics.CreateAPIView):
    """Driver registration endpoint"""

    serializer_class = DriverRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        driver = serializer.save()

        # Log the user in
        login(request, driver.user)

        return Response({
            'user': UserSerializer(driver.user).data,
            'driver': DriverSerializer(driver).data,
            'message': 'Driver registered and logged in successfully'
        }, status=status.HTTP_201_CREATED)


class PassengerRegistrationView(generics.CreateAPIView):
    """Passenger registration endpoint"""

    serializer_class = PassengerRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        passenger = serializer.save()

        # Log the user in
        login(request, passenger.user)

        return Response({
            'user': UserSerializer(passenger.user).data,
            'passenger': PassengerSerializer(passenger).data,
            'message': 'Passenger registered and logged in successfully'
        }, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login_view(request):
    """User login endpoint"""

    serializer = UserLoginSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    user = serializer.validated_data['user']
    login(request, user)

    return Response({
        'user': UserSerializer(user).data,
        'message': 'Login successful'
    })


@api_view(['POST'])
def logout_view(request):
    """User logout endpoint"""

    logout(request)
    return Response({'message': 'Successfully logged out'})


class UserProfileView(generics.RetrieveUpdateAPIView):
    """User profile view and update"""

    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


class UserUpdateView(generics.UpdateAPIView):
    """Update user information"""

    serializer_class = UserUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


@api_view(['POST'])
def change_password_view(request):
    """Change user password"""

    serializer = PasswordChangeSerializer(data=request.data, context={'request': request})
    serializer.is_valid(raise_exception=True)

    user = request.user
    user.set_password(serializer.validated_data['new_password'])
    user.save()

    return Response({'message': 'Password changed successfully'})


class UserStatsView(generics.RetrieveAPIView):
    """Get user statistics"""

    serializer_class = UserStatsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


class UserProfileDetailView(generics.RetrieveUpdateAPIView):
    """User profile details view"""

    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        profile, created = UserProfile.objects.get_or_create(user=self.request.user)
        return profile


class VehicleListCreateView(generics.ListCreateAPIView):
    """List and create user vehicles"""

    serializer_class = VehicleSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Only drivers can have vehicles
        if self.request.user.role == 'driver':
            try:
                driver = self.request.user.driver_profile
                return Vehicle.objects.filter(driver=driver)
            except Driver.DoesNotExist:
                return Vehicle.objects.none()
        return Vehicle.objects.none()

    def perform_create(self, serializer):
        # Only drivers can create vehicles
        if self.request.user.role != 'driver':
            raise permissions.PermissionDenied("Only drivers can add vehicles")

        try:
            driver = self.request.user.driver_profile
            serializer.save(driver=driver)
        except Driver.DoesNotExist:
            raise permissions.PermissionDenied("Driver profile not found")


class VehicleDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Vehicle detail, update, and delete"""

    serializer_class = VehicleSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Only drivers can access vehicles
        if self.request.user.role == 'driver':
            try:
                driver = self.request.user.driver_profile
                return Vehicle.objects.filter(driver=driver)
            except Driver.DoesNotExist:
                return Vehicle.objects.none()
        return Vehicle.objects.none()


@api_view(['GET'])
def user_detail_view(request, user_id):
    """Get public user information"""

    user = get_object_or_404(User, id=user_id)

    # Return limited public information
    data = {
        'id': user.id,
        'full_name': user.full_name,
        'avatar': user.avatar.url if user.avatar else None,
        'rating': user.rating,
        'total_ratings': user.total_ratings,
        'rides_offered': user.rides_offered,
        'rides_completed': user.rides_completed,
        'city': user.city,
        'country': user.country,
        'created_at': user.created_at,
    }

    return Response(data)


@api_view(['POST'])
def add_loyalty_points_view(request):
    """Add loyalty points to user account"""

    points = request.data.get('points', 0)
    if points > 0:
        request.user.add_loyalty_points(points)
        return Response({
            'message': f'{points} loyalty points added',
            'total_points': request.user.loyalty_points
        })

    return Response({'error': 'Invalid points value'}, status=status.HTTP_400_BAD_REQUEST)


class RideRatingListCreateView(generics.ListCreateAPIView):
    """List and create ride ratings"""

    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return RideRatingCreateSerializer
        return RideRatingSerializer

    def get_queryset(self):
        # Get ratings for the current user (both given and received)
        user = self.request.user
        return RideRating.objects.filter(
            models.Q(rater=user) | models.Q(rated_user=user)
        ).order_by('-created_at')


class UserRatingsView(generics.ListAPIView):
    """Get ratings for a specific user"""

    serializer_class = RideRatingSerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        user_id = self.kwargs['user_id']
        return RideRating.objects.filter(
            rated_user_id=user_id
        ).order_by('-created_at')


@api_view(['GET'])
def driver_dashboard_view(request):
    """Get driver dashboard data"""

    if request.user.role != 'driver':
        return Response({'error': 'Only drivers can access this endpoint'},
                       status=status.HTTP_403_FORBIDDEN)

    try:
        driver = request.user.driver_profile

        # Get driver statistics
        from rides.models import Ride, RidePending

        active_rides = Ride.objects.filter(driver=request.user, status='active').count()
        pending_bookings = RidePending.objects.filter(
            ride__driver=request.user,
            status='pending'
        ).count()

        data = {
            'driver_info': DriverSerializer(driver).data,
            'vehicles': VehicleSerializer(driver.vehicles.filter(is_active=True), many=True).data,
            'active_rides': active_rides,
            'pending_bookings': pending_bookings,
            'total_earnings': float(driver.total_earnings),
            'total_rides_offered': driver.total_rides_offered,
        }

        return Response(data)

    except Driver.DoesNotExist:
        return Response({'error': 'Driver profile not found'},
                       status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
def passenger_dashboard_view(request):
    """Get passenger dashboard data"""

    if request.user.role != 'passenger':
        return Response({'error': 'Only passengers can access this endpoint'},
                       status=status.HTTP_403_FORBIDDEN)

    try:
        passenger = request.user.passenger_profile

        # Get passenger statistics
        from rides.models import RideBooking, RidePending

        active_bookings = RideBooking.objects.filter(
            user=request.user,
            status='confirmed'
        ).count()
        pending_requests = RidePending.objects.filter(
            passenger=request.user,
            status='pending'
        ).count()

        data = {
            'passenger_info': PassengerSerializer(passenger).data,
            'active_bookings': active_bookings,
            'pending_requests': pending_requests,
            'total_spent': float(passenger.total_spent),
            'total_rides_booked': passenger.total_rides_booked,
        }

        return Response(data)

    except Passenger.DoesNotExist:
        return Response({'error': 'Passenger profile not found'},
                       status=status.HTTP_404_NOT_FOUND)
