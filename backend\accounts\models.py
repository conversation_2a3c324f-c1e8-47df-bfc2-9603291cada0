from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from PIL import Image
import os


class User(AbstractUser):
    """Custom user model extending Django's AbstractUser"""

    # User roles - removed 'both' option as per requirements
    ROLE_CHOICES = [
        ('passenger', 'Passenger'),
        ('driver', 'Driver'),
        ('admin', 'Admin'),
    ]

    email = models.EmailField(unique=True)
    full_name = models.CharField(max_length=255, blank=True)
    phone_number = models.CharField(max_length=20, blank=True)
    date_of_birth = models.DateField(blank=True, null=True)
    bio = models.TextField(max_length=500, blank=True)
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)

    # User role - must be selected during signup, no default
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)

    # Location information
    city = models.CharField(max_length=100, blank=True)
    country = models.CharField(max_length=100, default='Tunisia')

    # Ride statistics
    rides_offered = models.PositiveIntegerField(default=0)
    rides_completed = models.PositiveIntegerField(default=0)
    loyalty_points = models.PositiveIntegerField(default=100)
    referrals = models.PositiveIntegerField(default=0)

    # Rating system
    rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=5.0,
        validators=[MinValueValidator(0), MaxValueValidator(5)]
    )
    total_ratings = models.PositiveIntegerField(default=0)

    # Account settings
    is_verified = models.BooleanField(default=False)
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        db_table = 'users'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return self.email

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # Resize avatar image if it exists
        if self.avatar:
            img = Image.open(self.avatar.path)
            if img.height > 300 or img.width > 300:
                output_size = (300, 300)
                img.thumbnail(output_size)
                img.save(self.avatar.path)

    @property
    def ride_count(self):
        """Total number of rides (offered + completed as passenger)"""
        return self.rides_offered + self.rides_completed

    def update_rating(self, new_rating):
        """Update user's average rating"""
        total_score = self.rating * self.total_ratings + new_rating
        self.total_ratings += 1
        self.rating = total_score / self.total_ratings
        self.save()

    def add_loyalty_points(self, points):
        """Add loyalty points to user account"""
        self.loyalty_points += points
        self.save()


class UserProfile(models.Model):
    """Extended profile information for users"""

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')

    # Preferences
    preferred_language = models.CharField(
        max_length=10,
        choices=[('en', 'English'), ('fr', 'French'), ('ar', 'Arabic')],
        default='en'
    )
    preferred_currency = models.CharField(max_length=3, default='TND')

    # Driver information
    is_driver = models.BooleanField(default=False)
    driver_license_number = models.CharField(max_length=50, blank=True)
    driver_license_expiry = models.DateField(blank=True, null=True)

    # Emergency contact
    emergency_contact_name = models.CharField(max_length=255, blank=True)
    emergency_contact_phone = models.CharField(max_length=20, blank=True)

    # Social links
    facebook_profile = models.URLField(blank=True)
    linkedin_profile = models.URLField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_profiles'

    def __str__(self):
        return f"{self.user.email} Profile"


class Vehicle(models.Model):
    """User's vehicle information"""

    VEHICLE_TYPES = [
        ('sedan', 'Sedan'),
        ('hatchback', 'Hatchback'),
        ('suv', 'SUV'),
        ('coupe', 'Coupe'),
        ('convertible', 'Convertible'),
        ('wagon', 'Wagon'),
        ('van', 'Van'),
        ('pickup', 'Pickup Truck'),
    ]

    driver = models.ForeignKey('Driver', on_delete=models.CASCADE, related_name='vehicles')
    make = models.CharField(max_length=50)  # e.g., Toyota, Peugeot
    model = models.CharField(max_length=50)  # e.g., Corolla, 308
    year = models.PositiveIntegerField()
    color = models.CharField(max_length=30)
    license_plate = models.CharField(max_length=20, unique=True)
    vehicle_type = models.CharField(max_length=20, choices=VEHICLE_TYPES, default='sedan')
    seats = models.PositiveIntegerField(default=4, validators=[MinValueValidator(2), MaxValueValidator(8)])

    # Features
    has_ac = models.BooleanField(default=True)
    has_music = models.BooleanField(default=True)
    is_smoking_allowed = models.BooleanField(default=False)
    has_wifi = models.BooleanField(default=False)
    has_gps = models.BooleanField(default=False)
    has_phone_charger = models.BooleanField(default=False)

    # Insurance and registration
    insurance_expiry = models.DateField(blank=True, null=True)
    registration_expiry = models.DateField(blank=True, null=True)

    # Vehicle verification
    is_verified = models.BooleanField(default=False)
    verification_date = models.DateTimeField(null=True, blank=True)
    verification_notes = models.TextField(blank=True)

    # Vehicle photos
    front_photo = models.ImageField(upload_to='vehicles/photos/', blank=True, null=True)
    side_photo = models.ImageField(upload_to='vehicles/photos/', blank=True, null=True)
    interior_photo = models.ImageField(upload_to='vehicles/photos/', blank=True, null=True)

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'vehicles'
        unique_together = ['driver', 'license_plate']

    def __str__(self):
        return f"{self.year} {self.make} {self.model} ({self.license_plate})"

    @property
    def full_name(self):
        return f"{self.year} {self.make} {self.model}"

    @property
    def features_list(self):
        """Return a list of vehicle features"""
        features = []
        if self.has_ac:
            features.append('Air Conditioning')
        if self.has_music:
            features.append('Music System')
        if self.has_wifi:
            features.append('WiFi')
        if self.has_gps:
            features.append('GPS Navigation')
        if self.has_phone_charger:
            features.append('Phone Charger')
        if not self.is_smoking_allowed:
            features.append('Non-Smoking')
        return features


class Driver(models.Model):
    """Driver-specific information"""

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='driver_profile')
    driver_license_number = models.CharField(max_length=50, unique=True)
    license_expiry_date = models.DateField()

    # Driver statistics
    total_rides_offered = models.PositiveIntegerField(default=0)
    total_earnings = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    # Verification status
    is_license_verified = models.BooleanField(default=False)
    verification_date = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'drivers'

    def __str__(self):
        return f"Driver: {self.user.full_name} ({self.driver_license_number})"


class Passenger(models.Model):
    """Passenger-specific information"""

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='passenger_profile')
    id_card_number = models.CharField(max_length=50, unique=True)

    # Passenger statistics
    total_rides_booked = models.PositiveIntegerField(default=0)
    total_spent = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    # Preferences
    preferred_car_type = models.CharField(max_length=50, blank=True)
    smoking_preference = models.BooleanField(default=False)  # True if they prefer smoking allowed

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'passengers'

    def __str__(self):
        return f"Passenger: {self.user.full_name} ({self.id_card_number})"


class RideRating(models.Model):
    """Model for ride ratings and reviews"""

    RATING_CHOICES = [
        (1, '1 Star'),
        (2, '2 Stars'),
        (3, '3 Stars'),
        (4, '4 Stars'),
        (5, '5 Stars'),
    ]

    # The ride being rated
    ride = models.ForeignKey('rides.Ride', on_delete=models.CASCADE, related_name='ratings')

    # Who is giving the rating
    rater = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ratings_given')

    # Who is being rated
    rated_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ratings_received')

    # Rating details
    rating = models.PositiveIntegerField(choices=RATING_CHOICES)
    review = models.TextField(blank=True)

    # Rating categories
    punctuality = models.PositiveIntegerField(choices=RATING_CHOICES, null=True, blank=True)
    communication = models.PositiveIntegerField(choices=RATING_CHOICES, null=True, blank=True)
    cleanliness = models.PositiveIntegerField(choices=RATING_CHOICES, null=True, blank=True)
    safety = models.PositiveIntegerField(choices=RATING_CHOICES, null=True, blank=True)

    # Metadata
    is_anonymous = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ride_ratings'
        unique_together = ['ride', 'rater', 'rated_user']
        indexes = [
            models.Index(fields=['rated_user', '-created_at']),
            models.Index(fields=['rater', '-created_at']),
            models.Index(fields=['ride']),
        ]

    def __str__(self):
        return f"{self.rater.full_name} rated {self.rated_user.full_name}: {self.rating} stars"

    def save(self, *args, **kwargs):
        """Update user's overall rating when a new rating is saved"""
        super().save(*args, **kwargs)

        # Recalculate the rated user's average rating
        from django.db.models import Avg
        avg_rating = RideRating.objects.filter(rated_user=self.rated_user).aggregate(
            avg=Avg('rating')
        )['avg']

        if avg_rating:
            self.rated_user.rating = round(avg_rating, 2)
            self.rated_user.total_ratings = RideRating.objects.filter(rated_user=self.rated_user).count()
            self.rated_user.save(update_fields=['rating', 'total_ratings'])
